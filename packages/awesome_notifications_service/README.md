# Awesome Notifications Service

A comprehensive notification service package for the Salawati app using awesome_notifications.

## Features

- 🔔 **Multiple Notification Channels**: Separate channels for Athan, Dhikr, and General notifications
- ⏰ **Scheduled Notifications**: Support for precise scheduling with timezone awareness
- 🎵 **Custom Sounds**: Support for custom notification sounds
- 📱 **Cross-Platform**: Works on both Android and iOS
- 🔧 **Permission Management**: Automatic permission handling
- 📊 **Batch Processing**: Efficient batch scheduling of notifications
- 🎯 **Payload-based Management**: Easy management of notifications by type

## Installation

Add this package to your `pubspec.yaml`:

```yaml
dependencies:
  awesome_notifications_service:
    path: packages/awesome_notifications_service
```

## Usage

### Initialize the Service

```dart
import 'package:awesome_notifications_service/awesome_notifications_service.dart';

// Initialize the notification service
await NotificationService.instance.initialize();
```

### Schedule a Notification

```dart
final notification = NotificationData(
  title: 'Prayer Time',
  subtitle: 'Time for Fajr prayer',
  time: DateTime.now().add(Duration(minutes: 5)),
  payload: NotificationConstants.athanNotificationsPayload,
  channelKey: NotificationConstants.athanChannelKey,
);

await NotificationService.instance.scheduleNotification(notification);
```

### Schedule Dhikr Notifications

```dart
final utility = NotificationUtility();
await utility.scheduleDhikrNotifications();
```

### Cancel Notifications

```dart
// Cancel all notifications
await NotificationService.instance.cancelAllNotifications();

// Cancel by payload type
await NotificationService.instance.cancelNotificationsByPayload(
  NotificationConstants.athanNotificationsPayload
);

// Cancel specific notification
await NotificationService.instance.cancelNotification(notificationId);
```

### Check Permissions

```dart
final permissionStatus = await NotificationService.instance.requestPermissions();
if (permissionStatus.hasAllPermissions) {
  // Proceed with scheduling notifications
}
```

## Notification Types

### Athan Notifications
- Channel: `athan_channel`
- High priority with alarm sound
- Critical alerts enabled
- Full screen intent support

### Dhikr Notifications
- Channel: `dhikr_channel`
- Medium priority
- Custom dhikr sounds
- Scheduled throughout the day

### General Notifications
- Channel: `general_channel`
- Default priority
- General app notifications

## Models

### NotificationData
```dart
NotificationData(
  title: 'Notification Title',
  subtitle: 'Notification Body',
  time: DateTime.now().add(Duration(hours: 1)),
  payload: 'notification_type',
  sound: 'resource://raw/custom_sound',
  channelKey: 'channel_key',
  groupKey: 'group_key',
  id: 12345,
)
```

### Dhikr
```dart
Dhikr(
  subtitle: 'سبحان الله',
  sound: 'sou_tasbeeh',
)
```

## Constants

All notification-related constants are available in `NotificationConstants`:

- Channel keys and names
- Payload types
- Action keys
- Sound resources
- Default values

## Error Handling

The service includes comprehensive error handling with debug logging. All methods return appropriate success/failure indicators.

## Platform Support

- ✅ Android (API 21+)
- ✅ iOS (10.0+)
- ⚠️ Web (Limited support)
- ❌ Desktop (Not supported)

## Dependencies

- `awesome_notifications: ^0.9.3+1`
- `timezone: ^0.10.1`
- `get: ^4.7.2`
- `get_storage: ^2.1.1`
