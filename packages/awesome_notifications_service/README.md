# Awesome Notifications Service

A comprehensive notification service package using awesome_notifications for Salawati app. This package provides a complete, self-contained notification solution with all necessary permissions and configurations built-in.

## 🚀 Features

- ✅ **Self-contained**: All permissions and configurations included in the package
- ✅ **Local notifications** with custom sounds and layouts
- ✅ **Scheduled notifications** with precise timing
- ✅ **Multiple notification channels** for different types of notifications
- ✅ **Permission management** with user-friendly dialogs
- ✅ **Background notification handling**
- ✅ **Dhikr and prayer time notifications**
- ✅ **Custom notification sounds** for different Islamic phrases
- ✅ **Batch notification scheduling**
- ✅ **Notification grouping and categorization**

## 📋 Requirements

### Android Requirements (Automatically Configured)
- ✅ Minimum SDK: 23 (Android 6.0)
- ✅ Target SDK: 34 (Android 14)
- ✅ Compile SDK: 34+
- ✅ Compatible with intl 0.19.0 (Flutter SDK requirement)
- ✅ All required permissions automatically added:
  - `android.permission.VIBRATE`
  - `android.permission.RECEIVE_BOOT_COMPLETED`
  - `android.permission.WAKE_LOCK`
  - `android.permission.FOREGROUND_SERVICE`
  - `android.permission.SCHEDULE_EXACT_ALARM`
  - `android.permission.USE_FULL_SCREEN_INTENT`
  - `android.permission.POST_NOTIFICATIONS`

### iOS Requirements (Automatically Configured)
- ✅ Minimum iOS: 12.0
- ✅ Notification permissions automatically requested
- ✅ Background app refresh capabilities

## 🛠 Installation

Add this package to your `pubspec.yaml`:

```yaml
dependencies:
  awesome_notifications_service:
    path: packages/awesome_notifications_service
```

## 📱 Usage

### Basic Setup

```dart
import 'package:awesome_notifications_service/awesome_notifications_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the notification service
  await AwesomeNotificationsService.initialize();

  runApp(MyApp());
}
```

### Creating Notifications

```dart
// Create a simple notification
await AwesomeNotificationsService.createNotification(
  content: NotificationContent(
    id: 1,
    channelKey: 'basic_channel',
    title: 'Prayer Time',
    body: 'It\'s time for Maghrib prayer',
    notificationLayout: NotificationLayout.Default,
  ),
);

// Create a notification with custom sound
await AwesomeNotificationsService.createNotification(
  content: NotificationContent(
    id: 2,
    channelKey: 'athan_channel',
    title: 'Athan',
    body: 'Allahu Akbar',
    customSound: 'resource://raw/athan_sound',
  ),
);
```

### Scheduling Notifications

```dart
// Schedule a notification
await AwesomeNotificationsService.createNotification(
  content: NotificationContent(
    id: 3,
    channelKey: 'dhikr_channel',
    title: 'Dhikr Reminder',
    body: 'Time for your daily dhikr',
  ),
  schedule: NotificationCalendar(
    hour: 9,
    minute: 0,
    second: 0,
    repeats: true,
  ),
);
```

### Permission Management

```dart
// Request permissions
bool isAllowed = await AwesomeNotificationsService.requestPermissions();

// Check if notifications are allowed
bool areNotificationsAllowed = await AwesomeNotificationsService.isNotificationAllowed();
```

## 🔧 Configuration

This package is **self-contained** and automatically configures all necessary permissions and settings. No manual configuration is required in your main app!

### What's Automatically Configured:

#### Android:
- All required permissions in AndroidManifest.xml
- Foreground service configuration
- Notification channels setup
- Boot receiver for scheduled notifications

#### iOS:
- Minimum deployment target (iOS 12.0)
- Notification capabilities
- Background processing permissions

## 📚 API Reference

### NotificationService

The main service class that provides all notification functionality.

#### Methods:

- `initialize()` - Initialize the notification service
- `createNotification()` - Create and display a notification
- `scheduleNotification()` - Schedule a notification for later
- `cancelNotification()` - Cancel a specific notification
- `cancelAllNotifications()` - Cancel all notifications
- `requestPermissions()` - Request notification permissions
- `isNotificationAllowed()` - Check if notifications are allowed

### Notification Channels

The package provides three pre-configured channels:

1. **Athan Channel** (`athan_channel`)
   - High priority
   - Custom athan sounds
   - Full-screen intent capability

2. **Dhikr Channel** (`dhikr_channel`)
   - Medium priority
   - Custom dhikr sounds
   - Vibration patterns

3. **General Channel** (`general_channel`)
   - Default priority
   - Standard notification sounds

## 🎵 Custom Sounds

The package supports custom sounds for different notification types:

```dart
// Use custom sound from assets
customSound: 'resource://raw/my_sound'

// Use system default
customSound: null
```

## 🔔 Notification Types

- **Basic Notifications**: Simple title and body
- **Big Picture**: Notifications with images
- **Progress**: Show progress bars (Android only)
- **Inbox**: Multiple lines of text
- **Messaging**: Chat-like notifications

## 🚨 Troubleshooting

### Common Issues:

1. **Notifications not showing**: Check if permissions are granted
2. **Custom sounds not playing**: Ensure sound files are in the correct format
3. **Scheduled notifications not working**: Verify device battery optimization settings

### Debug Mode:

Enable debug mode to see detailed logs:

```dart
await AwesomeNotificationsService.initialize(debug: true);
```

## 📄 License

This package is part of the Salawati app project.

## 🤝 Contributing

This package is specifically designed for the Salawati app. For contributions, please contact the development team.
