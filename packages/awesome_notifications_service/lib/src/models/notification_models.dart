import 'package:awesome_notifications/awesome_notifications.dart';

/// Data model for notification information
class NotificationData {
  final String title;
  final String subtitle;
  final DateTime time;
  final String? payload;
  final String? sound;
  final int? id;
  final String? channelKey;
  final String? groupKey;
  final Map<String, String>? customData;

  const NotificationData({
    required this.title,
    required this.subtitle,
    required this.time,
    this.payload,
    this.sound,
    this.id,
    this.channelKey,
    this.groupKey,
    this.customData,
  });

  /// Convert to AwesomeNotifications format
  NotificationContent toNotificationContent() {
    return NotificationContent(
      id: id ?? DateTime.now().millisecondsSinceEpoch % 100000,
      channelKey: channelKey ?? 'general_channel',
      groupKey: groupKey,
      title: title,
      body: subtitle,
      payload: payload != null ? {'type': payload!, ...?customData} : customData,
      customSound: sound,
      notificationLayout: NotificationLayout.Default,
      category: NotificationCategory.Alarm,
      wakeUpScreen: true,
      fullScreenIntent: true,
      criticalAlert: true,
    );
  }

  /// Convert to AwesomeNotifications scheduled format
  NotificationSchedule toNotificationSchedule() {
    return NotificationCalendar.fromDate(
      date: time,
      allowWhileIdle: true,
      preciseAlarm: true,
    );
  }

  NotificationData copyWith({
    String? title,
    String? subtitle,
    DateTime? time,
    String? payload,
    String? sound,
    int? id,
    String? channelKey,
    String? groupKey,
    Map<String, String>? customData,
  }) {
    return NotificationData(
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      time: time ?? this.time,
      payload: payload ?? this.payload,
      sound: sound ?? this.sound,
      id: id ?? this.id,
      channelKey: channelKey ?? this.channelKey,
      groupKey: groupKey ?? this.groupKey,
      customData: customData ?? this.customData,
    );
  }

  @override
  String toString() {
    return 'NotificationData(title: $title, subtitle: $subtitle, time: $time, payload: $payload)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationData &&
        other.title == title &&
        other.subtitle == subtitle &&
        other.time == time &&
        other.payload == payload &&
        other.sound == sound &&
        other.id == id &&
        other.channelKey == channelKey &&
        other.groupKey == groupKey;
  }

  @override
  int get hashCode {
    return Object.hash(
      title,
      subtitle,
      time,
      payload,
      sound,
      id,
      channelKey,
      groupKey,
    );
  }
}

/// Model for Dhikr data
class Dhikr {
  final String subtitle;
  final String sound;

  const Dhikr({
    required this.subtitle,
    required this.sound,
  });

  @override
  String toString() => 'Dhikr(subtitle: $subtitle, sound: $sound)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Dhikr && other.subtitle == subtitle && other.sound == sound;
  }

  @override
  int get hashCode => Object.hash(subtitle, sound);
}

/// Model for notification permission status
class NotificationPermissionStatus {
  final bool isGranted;
  final bool canScheduleExactAlarms;
  final bool canShowNotifications;
  final bool canPlaySounds;
  final String? deniedReason;

  const NotificationPermissionStatus({
    required this.isGranted,
    required this.canScheduleExactAlarms,
    required this.canShowNotifications,
    required this.canPlaySounds,
    this.deniedReason,
  });

  bool get hasAllPermissions =>
      isGranted && canScheduleExactAlarms && canShowNotifications;

  @override
  String toString() {
    return 'NotificationPermissionStatus(isGranted: $isGranted, canScheduleExactAlarms: $canScheduleExactAlarms, canShowNotifications: $canShowNotifications, canPlaySounds: $canPlaySounds)';
  }
}

/// Enum for notification types
enum NotificationType {
  athan,
  dhikr,
  general,
  warning,
}

/// Extension for notification type utilities
extension NotificationTypeExtension on NotificationType {
  String get channelKey {
    switch (this) {
      case NotificationType.athan:
        return 'athan_channel';
      case NotificationType.dhikr:
        return 'dhikr_channel';
      case NotificationType.warning:
      case NotificationType.general:
        return 'general_channel';
    }
  }

  String get groupKey {
    switch (this) {
      case NotificationType.athan:
        return 'athan_group';
      case NotificationType.dhikr:
        return 'dhikr_group';
      case NotificationType.warning:
      case NotificationType.general:
        return 'general_group';
    }
  }

  int get baseId {
    switch (this) {
      case NotificationType.athan:
        return 2000;
      case NotificationType.dhikr:
        return 3000;
      case NotificationType.warning:
        return 9000;
      case NotificationType.general:
        return 1000;
    }
  }
}
