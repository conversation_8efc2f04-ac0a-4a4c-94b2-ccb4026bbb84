import 'dart:io';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
import 'package:timezone/timezone.dart' as tz;

import '../models/notification_models.dart';
import '../constants/notification_constants.dart';

/// Main notification service using Flutter Local Notifications
class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();

  NotificationService._();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  /// Initialize the notification service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
        requestCriticalPermission: true,
      );

      // Combined initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize the plugin
      final bool? initialized = await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      if (initialized == true) {
        await _createNotificationChannels();
        _isInitialized = true;

        // Request permissions after initialization
        await Future.delayed(const Duration(seconds: 1));
        await requestPermissions();
      }

      return initialized ?? false;
    } catch (e) {
      debugPrint('Error initializing notifications: $e');
      return false;
    }
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    if (Platform.isAndroid) {
      // Athan channel
      const AndroidNotificationChannel athanChannel = AndroidNotificationChannel(
        NotificationConstants.athanChannelKey,
        NotificationConstants.athanChannelName,
        description: NotificationConstants.athanChannelDescription,
        importance: Importance.max,
        playSound: true,
        enableVibration: true,
        enableLights: true,
        ledColor: Color(0xFF9D50DD),
        showBadge: true,
      );

      // Create channels
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(athanChannel);
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    debugPrint('Notification tapped: ${notificationResponse.payload}');
  }

  /// Request notification permissions
  Future<NotificationPermissionStatus> requestPermissions() async {
    try {
      bool isGranted = false;
      bool canScheduleExactAlarms = true;

      if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        // Request notification permission
        final bool? granted = await androidImplementation?.requestNotificationsPermission();
        isGranted = granted ?? false;

        // Request exact alarm permission
        final bool? exactAlarmGranted = await androidImplementation?.requestExactAlarmsPermission();
        canScheduleExactAlarms = exactAlarmGranted ?? false;
      } else if (Platform.isIOS) {
        final IOSFlutterLocalNotificationsPlugin? iosImplementation =
            _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>();

        final bool? granted = await iosImplementation?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
          critical: true,
        );
        isGranted = granted ?? false;
      }

      return NotificationPermissionStatus(
        isGranted: isGranted,
        canScheduleExactAlarms: canScheduleExactAlarms,
        canShowNotifications: isGranted,
        canPlaySounds: isGranted,
      );
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      return NotificationPermissionStatus(
        isGranted: false,
        canScheduleExactAlarms: false,
        canShowNotifications: false,
        canPlaySounds: false,
        deniedReason: 'Error requesting permissions: $e',
      );
    }
  }

  /// Check if notifications are allowed
  Future<bool> areNotificationsAllowed() async {
    if (Platform.isAndroid) {
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.areNotificationsEnabled() ?? false;
    }
    return false;
  }

  /// Send an immediate notification
  Future<bool> sendNotification(NotificationData notificationData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      const androidDetails = AndroidNotificationDetails(
        'general_channel',
        'General Notifications',
        channelDescription: 'General app notifications',
        importance: Importance.defaultImportance,
        priority: Priority.high,
        playSound: true,
        enableVibration: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const platformDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _flutterLocalNotificationsPlugin.show(
        notificationData.id ?? DateTime.now().millisecondsSinceEpoch,
        notificationData.title,
        notificationData.subtitle,
        platformDetails,
        payload: notificationData.payload,
      );

      return true;
    } catch (e) {
      debugPrint('Error sending notification: $e');
      return false;
    }
  }

  /// Schedule a notification
  Future<bool> scheduleNotification(NotificationData notificationData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (notificationData.time == null) {
        return await sendNotification(notificationData);
      }

      final scheduledDate = tz.TZDateTime.from(notificationData.time!, tz.local);

      const androidDetails = AndroidNotificationDetails(
        'general_channel',
        'General Notifications',
        channelDescription: 'General app notifications',
        importance: Importance.defaultImportance,
        priority: Priority.high,
        playSound: true,
        enableVibration: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const platformDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        notificationData.id ?? DateTime.now().millisecondsSinceEpoch,
        notificationData.title,
        notificationData.subtitle,
        scheduledDate,
        platformDetails,
        payload: notificationData.payload,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      );

      return true;
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
      return false;
    }
  }

  /// Cancel a specific notification
  Future<bool> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
      return true;
    } catch (e) {
      debugPrint('Error canceling notification: $e');
      return false;
    }
  }

  /// Cancel all notifications
  Future<bool> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
      return true;
    } catch (e) {
      debugPrint('Error canceling all notifications: $e');
      return false;
    }
  }

  /// Get all scheduled notifications
  Future<List<PendingNotificationRequest>> getScheduledNotifications() async {
    try {
      return await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
    } catch (e) {
      debugPrint('Error getting scheduled notifications: $e');
      return [];
    }
  }

  /// Schedule multiple notifications in batches
  Future<void> scheduleNotificationsBatch(
    List<NotificationData> notifications,
    String defaultPayload,
  ) async {
    const int batchSize = 50; // Default batch size

    for (var i = 0; i < notifications.length; i += batchSize) {
      final batch = notifications.sublist(
        i,
        i + batchSize > notifications.length ? notifications.length : i + batchSize,
      );

      for (var notification in batch) {
        final notificationWithPayload = notification.payload != null
            ? notification
            : notification.copyWith(payload: defaultPayload);

        await scheduleNotification(notificationWithPayload);
      }
    }
  }

  /// Cancel notifications by payload type
  Future<void> cancelNotificationsByPayload(String payload) async {
    try {
      final pendingNotifications = await _flutterLocalNotificationsPlugin.pendingNotificationRequests();

      for (final notification in pendingNotifications) {
        if (notification.payload?.contains(payload) == true) {
          await cancelNotification(notification.id);
        }
      }
    } catch (e) {
      debugPrint('Error canceling notifications by payload: $e');
    }
  }

  /// Cancel old notifications and schedule new ones
  Future<void> cancelOldNotificationsAndScheduleNewOnes(
    String payload,
    Future<void> Function() scheduleNewNotifications,
  ) async {
    try {
      // Cancel existing notifications with the same payload
      await cancelNotificationsByPayload(payload);

      // Schedule new notifications
      await scheduleNewNotifications();
    } catch (e) {
      debugPrint('Error in cancelOldNotificationsAndScheduleNewOnes: $e');
    }
  }

  /// Check if there are scheduled notifications with specific payload
  Future<bool> hasScheduledNotifications(String payload) async {
    try {
      final scheduledNotifications = await getScheduledNotifications();
      return scheduledNotifications.any(
        (notification) => notification.payload?.contains(payload) == true,
      );
    } catch (e) {
      debugPrint('Error checking scheduled notifications: $e');
      return false;
    }
  }

  /// Get notification count by payload
  Future<int> getNotificationCountByPayload(String payload) async {
    try {
      final scheduledNotifications = await getScheduledNotifications();
      return scheduledNotifications
          .where((notification) => notification.payload?.contains(payload) == true)
          .length;
    } catch (e) {
      debugPrint('Error getting notification count: $e');
      return 0;
    }
  }

  /// Open notification settings
  Future<void> openNotificationSettings() async {
    try {
      if (Platform.isAndroid) {
        // For flutter_local_notifications, we can't directly open settings
        // This is a placeholder for future implementation
        debugPrint('Opening notification settings not directly supported');
      }
    } catch (e) {
      debugPrint('Error opening notification settings: $e');
    }
  }

  /// Open alarm settings (Android only)
  Future<void> openAlarmSettings() async {
    try {
      if (Platform.isAndroid) {
        // For flutter_local_notifications, we can't directly open alarm settings
        // This is a placeholder for future implementation
        debugPrint('Opening alarm settings not directly supported');
      }
    } catch (e) {
      debugPrint('Error opening alarm settings: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
  }
}