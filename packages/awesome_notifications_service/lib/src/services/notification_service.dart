import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import '../constants/notification_constants.dart';
import '../models/notification_models.dart';

/// Main notification service using Awesome Notifications
class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();

  NotificationService._();

  bool _isInitialized = false;

  /// Early initialization method to be called in main() function
  static Future<bool> initializeEarly() async {
    return await instance.initialize();
  }

  /// Initialize the notification service
  Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint('✅ Notification service already initialized');
      return true;
    }

    try {
      final isInitialized = await AwesomeNotifications().initialize(
        null, // Use default app icon
        [
          NotificationChannel(
            channelKey: NotificationConstants.athanChannelKey,
            channelName: NotificationConstants.athanChannelName,
            channelDescription: NotificationConstants.athanChannelDescription,
            defaultColor: const Color(0xFF9D50DD),
            ledColor: const Color(0xFF9D50DD),
            importance: NotificationImportance.Max,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: true,
            defaultRingtoneType: DefaultRingtoneType.Alarm,
          ),
          NotificationChannel(
            channelKey: NotificationConstants.dhikrChannelKey,
            channelName: NotificationConstants.dhikrChannelName,
            channelDescription: NotificationConstants.dhikrChannelDescription,
            defaultColor: const Color(0xFF4CAF50),
            ledColor: const Color(0xFF4CAF50),
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: false,
            defaultRingtoneType: DefaultRingtoneType.Notification,
          ),
          NotificationChannel(
            channelKey: NotificationConstants.generalChannelKey,
            channelName: NotificationConstants.generalChannelName,
            channelDescription: NotificationConstants.generalChannelDescription,
            defaultColor: const Color(0xFF2196F3),
            ledColor: const Color(0xFF2196F3),
            importance: NotificationImportance.Default,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: false,
            defaultRingtoneType: DefaultRingtoneType.Notification,
          ),
        ],
        channelGroups: [
          NotificationChannelGroup(
            channelGroupKey: 'athan_group',
            channelGroupName: 'Athan Notifications',
          ),
          NotificationChannelGroup(
            channelGroupKey: 'dhikr_group',
            channelGroupName: 'Dhikr Notifications',
          ),
        ],
        debug: kDebugMode,
      );

      if (isInitialized) {
        _setListeners();
        _isInitialized = true;
        debugPrint('✅ Awesome Notifications initialized successfully');
      } else {
        debugPrint('❌ Failed to initialize Awesome Notifications');
      }

      return isInitialized;
    } catch (e) {
      debugPrint('Error initializing notifications: $e');
      return false;
    }
  }

  /// Set up notification listeners
  void _setListeners() {
    AwesomeNotifications().setListeners(
      onActionReceivedMethod: _onActionReceived,
      onNotificationCreatedMethod: _onNotificationCreated,
      onNotificationDisplayedMethod: _onNotificationDisplayed,
      onDismissActionReceivedMethod: _onDismissActionReceived,
    );
  }

  /// Handle notification action received
  static Future<void> _onActionReceived(ReceivedAction receivedAction) async {
    debugPrint('Notification action received: ${receivedAction.payload}');

    final payload = receivedAction.payload;
    if (payload != null) {
      // Handle different notification types based on payload
      if (payload.containsKey(NotificationConstants.athanNotificationsPayload)) {
        // Handle athan notification action
      } else if (payload.containsKey(NotificationConstants.soundNotificationsPayload)) {
        // Handle dhikr notification action
      } else {
        // Handle general notification action
      }
    }
  }

  /// Handle notification created
  static Future<void> _onNotificationCreated(ReceivedNotification receivedNotification) async {
    debugPrint('Notification created: ${receivedNotification.payload}');
  }

  /// Handle notification displayed
  static Future<void> _onNotificationDisplayed(ReceivedNotification receivedNotification) async {
    debugPrint('Notification displayed: ${receivedNotification.payload}');
  }

  /// Handle notification dismissed
  static Future<void> _onDismissActionReceived(ReceivedAction receivedAction) async {
    debugPrint('Notification dismissed: ${receivedAction.payload}');
  }

  /// Request notification permissions
  Future<NotificationPermissionStatus> requestPermissions() async {
    try {
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (!isAllowed) {
        final granted = await AwesomeNotifications().requestPermissionToSendNotifications();
        return NotificationPermissionStatus(
          isGranted: granted,
          canScheduleExactAlarms: granted,
          canShowNotifications: granted,
          canPlaySounds: granted,
        );
      }

      return NotificationPermissionStatus(
        isGranted: true,
        canScheduleExactAlarms: true,
        canShowNotifications: true,
        canPlaySounds: true,
      );
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      return NotificationPermissionStatus(
        isGranted: false,
        canScheduleExactAlarms: false,
        canShowNotifications: false,
        canPlaySounds: false,
        deniedReason: 'Error requesting permissions: $e',
      );
    }
  }

  /// Check if notifications are allowed
  Future<bool> areNotificationsAllowed() async {
    return await AwesomeNotifications().isNotificationAllowed();
  }

  /// Send an immediate notification
  Future<bool> sendNotification(NotificationData notificationData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      debugPrint('🔔 Sending notification: ${notificationData.title}');
      debugPrint('🔊 Sound: ${notificationData.sound}');
      debugPrint('📱 Channel: ${notificationData.channelKey}');

      final result = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: notificationData.id ?? DateTime.now().millisecondsSinceEpoch,
          channelKey: notificationData.channelKey ?? NotificationConstants.generalChannelKey,
          title: notificationData.title,
          body: notificationData.subtitle,
          payload: notificationData.payload != null
              ? {'type': notificationData.payload, ...?notificationData.customData}
              : notificationData.customData,
          customSound: notificationData.sound,
          notificationLayout: NotificationLayout.Default,
          category: NotificationCategory.Alarm,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
        ),
        actionButtons: [
          NotificationActionButton(
            key: 'DISMISS',
            label: 'Dismiss',
            actionType: ActionType.DismissAction,
          ),
        ],
      );

      debugPrint('📤 Notification sent: $result');
      return result;
    } catch (e) {
      debugPrint('❌ Error sending notification: $e');
      return false;
    }
  }

  /// Schedule a notification
  Future<bool> scheduleNotification(NotificationData notificationData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (notificationData.time == null) {
        debugPrint('⏰ No time specified, sending immediate notification');
        return await sendNotification(notificationData);
      }

      debugPrint('⏰ Scheduling notification: ${notificationData.title}');
      debugPrint('🔊 Sound: ${notificationData.sound}');
      debugPrint('📅 Time: ${notificationData.time}');
      debugPrint('📱 Channel: ${notificationData.channelKey}');

      final result = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: notificationData.id ?? DateTime.now().millisecondsSinceEpoch,
          channelKey: notificationData.channelKey ?? NotificationConstants.generalChannelKey,
          title: notificationData.title,
          body: notificationData.subtitle,
          payload: notificationData.payload != null
              ? {'type': notificationData.payload, ...?notificationData.customData}
              : notificationData.customData,
          customSound: notificationData.sound,
          notificationLayout: NotificationLayout.Default,
          category: NotificationCategory.Alarm,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
        ),
        schedule: NotificationCalendar.fromDate(
          date: notificationData.time!,
          allowWhileIdle: true,
          preciseAlarm: true,
        ),
        actionButtons: [
          NotificationActionButton(
            key: 'DISMISS',
            label: 'Dismiss',
            actionType: ActionType.DismissAction,
          ),
        ],
      );

      debugPrint('📅 Notification scheduled: $result');
      return result;
    } catch (e) {
      debugPrint('❌ Error scheduling notification: $e');
      return false;
    }
  }

  /// Cancel a specific notification
  Future<bool> cancelNotification(int id) async {
    try {
      await AwesomeNotifications().cancel(id);
      return true;
    } catch (e) {
      debugPrint('Error canceling notification: $e');
      return false;
    }
  }

  /// Cancel all notifications
  Future<bool> cancelAllNotifications() async {
    try {
      await AwesomeNotifications().cancelAll();
      return true;
    } catch (e) {
      debugPrint('Error canceling all notifications: $e');
      return false;
    }
  }

  /// Get all scheduled notifications
  Future<List<NotificationModel>> getScheduledNotifications() async {
    try {
      return await AwesomeNotifications().listScheduledNotifications();
    } catch (e) {
      debugPrint('Error getting scheduled notifications: $e');
      return [];
    }
  }

  /// Schedule multiple notifications in batches
  Future<void> scheduleNotificationsBatch(
    List<NotificationData> notifications,
    String defaultPayload,
  ) async {
    const int batchSize = 50; // Default batch size

    for (var i = 0; i < notifications.length; i += batchSize) {
      final batch = notifications.sublist(
        i,
        i + batchSize > notifications.length ? notifications.length : i + batchSize,
      );

      for (var notification in batch) {
        final notificationWithPayload = notification.payload != null
            ? notification
            : notification.copyWith(payload: defaultPayload);

        await scheduleNotification(notificationWithPayload);
      }
    }
  }

  /// Cancel notifications by payload type
  Future<void> cancelNotificationsByPayload(String payload) async {
    try {
      final pendingNotifications = await getScheduledNotifications();

      for (final notification in pendingNotifications) {
        if (notification.content?.payload?.containsKey(payload) == true) {
          await cancelNotification(notification.content!.id!);
        }
      }
    } catch (e) {
      debugPrint('Error canceling notifications by payload: $e');
    }
  }

  /// Cancel old notifications and schedule new ones
  Future<void> cancelOldNotificationsAndScheduleNewOnes(
    String payload,
    Future<void> Function() scheduleNewNotifications,
  ) async {
    try {
      // Cancel existing notifications with the same payload
      await cancelNotificationsByPayload(payload);

      // Schedule new notifications
      await scheduleNewNotifications();
    } catch (e) {
      debugPrint('Error in cancelOldNotificationsAndScheduleNewOnes: $e');
    }
  }

  /// Check if there are scheduled notifications with specific payload
  Future<bool> hasScheduledNotifications(String payload) async {
    try {
      final scheduledNotifications = await getScheduledNotifications();
      return scheduledNotifications.any(
        (notification) => notification.content?.payload?.containsKey(payload) == true,
      );
    } catch (e) {
      debugPrint('Error checking scheduled notifications: $e');
      return false;
    }
  }

  /// Get notification count by payload
  Future<int> getNotificationCountByPayload(String payload) async {
    try {
      final scheduledNotifications = await getScheduledNotifications();
      return scheduledNotifications
          .where((notification) => notification.content?.payload?.containsKey(payload) == true)
          .length;
    } catch (e) {
      debugPrint('Error getting notification count: $e');
      return 0;
    }
  }

  /// Open notification settings
  Future<void> openNotificationSettings() async {
    try {
      await AwesomeNotifications().showNotificationConfigPage();
    } catch (e) {
      debugPrint('Error opening notification settings: $e');
    }
  }

  /// Open alarm settings (Android only)
  Future<void> openAlarmSettings() async {
    try {
      await AwesomeNotifications().showAlarmPage();
    } catch (e) {
      debugPrint('Error opening alarm settings: $e');
    }
  }

  /// Send a test notification to verify sound functionality
  Future<bool> sendTestNotification({String? sound}) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      debugPrint('🧪 Sending test notification with sound: $sound');

      return await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: 999999,
          channelKey: NotificationConstants.athanChannelKey,
          title: 'Test Notification',
          body: 'Testing sound: ${sound ?? 'default'}',
          customSound: sound,
          notificationLayout: NotificationLayout.Default,
          category: NotificationCategory.Alarm,
          wakeUpScreen: true,
          criticalAlert: true,
        ),
      );
    } catch (e) {
      debugPrint('❌ Error sending test notification: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
  }
}