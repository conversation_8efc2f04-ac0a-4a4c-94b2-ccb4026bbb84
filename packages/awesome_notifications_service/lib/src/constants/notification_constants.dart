/// Notification constants for the Salawati app
class NotificationConstants {
  // Notification Channels - must match main.dart initialization
  static const String athanChannelKey = 'salawati_athan';
  static const String athanChannelName = 'Athan Notifications';
  static const String athanChannelDescription = 'Prayer time notifications with athan sound';

  static const String dhikrChannelKey = 'salawati_dhikr';
  static const String dhikrChannelName = 'Dhikr Notifications';
  static const String dhikrChannelDescription = 'Dhikr reminder notifications';

  static const String generalChannelKey = 'salawati_general';
  static const String generalChannelName = 'General Notifications';
  static const String generalChannelDescription = 'General app notifications';

  // Notification Groups
  static const String athanGroupKey = 'athan_group';
  static const String dhikrGroupKey = 'dhikr_group';

  // Payload Types
  static const String athanNotificationsPayload = 'ATHAN_NOTIFICATIONS_PAYLOAD';
  static const String soundNotificationsPayload = 'SOUND_NOTIFICATIONS_PAYLOAD';
  static const String notificationsWarningPayload = 'NOTIFICATIONS_WARNING_PAYLOAD';

  // Action Keys
  static const String cancelActionKey = 'CANCEL_ACTION';
  static const String dismissActionKey = 'DISMISS_ACTION';

  // Notification IDs
  static const int baseNotificationId = 1000;
  static const int athanBaseId = 2000;
  static const int dhikrBaseId = 3000;
  static const int warningNotificationId = 9999;

  // Sound Resources - for awesome_notifications (no resource:// prefix needed)
  static const String defaultSound = 'default_sound';
  static const String athanSound1 = 'athan1_short';
  static const String athanSound2 = 'athan2_short';
  static const String athanSound3 = 'athan3_short';
  static const String athanSound4 = 'athan4_short';
  static const String birdSound = 'bird';
  static const String preAthanSound = 'pre_athan';
  static const String iqamaSound = 'iqama';

  // Cache Keys
  static const String startTimeKey = 'startTime';
  static const String endTimeKey = 'endTime';
  static const String notificationPermissionKey = 'notification_permission';

  // Time Constants
  static const int defaultStartHour = 7;
  static const int defaultStartMinute = 0;
  static const int defaultEndHour = 22;
  static const int defaultEndMinute = 0;
  static const int preNotificationMinutes = 15;
  static const int batchSize = 20;
  static const int maxNotificationDays = 7;
}
