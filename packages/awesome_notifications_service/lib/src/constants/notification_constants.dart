/// Notification constants for the Salawati app
class NotificationConstants {
  // Notification Channels
  static const String athanChannelKey = 'athan_channel';
  static const String athanChannelName = 'Athan Notifications';
  static const String athanChannelDescription = 'Prayer time notifications';

  static const String dhikrChannelKey = 'dhikr_channel';
  static const String dhikrChannelName = 'Dhikr Notifications';
  static const String dhikrChannelDescription = 'Audio Athkar notifications';

  static const String generalChannelKey = 'general_channel';
  static const String generalChannelName = 'General Notifications';
  static const String generalChannelDescription = 'General app notifications';

  // Notification Groups
  static const String athanGroupKey = 'athan_group';
  static const String dhikrGroupKey = 'dhikr_group';

  // Payload Types
  static const String athanNotificationsPayload = 'ATHAN_NOTIFICATIONS_PAYLOAD';
  static const String soundNotificationsPayload = 'SOUND_NOTIFICATIONS_PAYLOAD';
  static const String notificationsWarningPayload = 'NOTIFICATIONS_WARNING_PAYLOAD';

  // Action Keys
  static const String cancelActionKey = 'CANCEL_ACTION';
  static const String dismissActionKey = 'DISMISS_ACTION';

  // Notification IDs
  static const int baseNotificationId = 1000;
  static const int athanBaseId = 2000;
  static const int dhikrBaseId = 3000;
  static const int warningNotificationId = 9999;

  // Sound Resources
  static const String defaultSound = 'resource://raw/default_sound';
  static const String athanSound1 = 'resource://raw/athan1_short';
  static const String athanSound2 = 'resource://raw/athan2_short';
  static const String athanSound3 = 'resource://raw/athan3_short';
  static const String athanSound4 = 'resource://raw/athan4_short';
  static const String birdSound = 'resource://raw/bird';
  static const String preAthanSound = 'resource://raw/pre_athan';
  static const String iqamaSound = 'resource://raw/iqama';

  // Cache Keys
  static const String startTimeKey = 'startTime';
  static const String endTimeKey = 'endTime';
  static const String notificationPermissionKey = 'notification_permission';

  // Time Constants
  static const int defaultStartHour = 7;
  static const int defaultStartMinute = 0;
  static const int defaultEndHour = 22;
  static const int defaultEndMinute = 0;
  static const int preNotificationMinutes = 15;
  static const int batchSize = 20;
  static const int maxNotificationDays = 7;
}
