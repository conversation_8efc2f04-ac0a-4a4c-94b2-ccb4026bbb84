import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../models/notification_models.dart';
import '../constants/notification_constants.dart';
import '../services/notification_service.dart';

/// Utility class for managing notification scheduling and configuration
class NotificationUtility {
  final GetStorage _storage = GetStorage();
  final NotificationService _notificationService = NotificationService.instance;

  // Observable time range
  final Rx<TimeOfDay> _startTime = const TimeOfDay(
    hour: NotificationConstants.defaultStartHour,
    minute: NotificationConstants.defaultStartMinute,
  ).obs;

  final Rx<TimeOfDay> _endTime = const TimeOfDay(
    hour: NotificationConstants.defaultEndHour,
    minute: NotificationConstants.defaultEndMinute,
  ).obs;

  NotificationUtility() {
    _loadTimeRange();
  }

  // Getters
  TimeOfDay get startTime => _startTime.value;
  TimeOfDay get endTime => _endTime.value;

  /// Load saved time range from storage
  Future<void> _loadTimeRange() async {
    try {
      final startTimeData = _storage.read(NotificationConstants.startTimeKey);
      final endTimeData = _storage.read(NotificationConstants.endTimeKey);

      if (startTimeData != null) {
        _startTime.value = TimeOfDay(
          hour: startTimeData['hour'] ?? NotificationConstants.defaultStartHour,
          minute: startTimeData['minute'] ?? NotificationConstants.defaultStartMinute,
        );
      }

      if (endTimeData != null) {
        _endTime.value = TimeOfDay(
          hour: endTimeData['hour'] ?? NotificationConstants.defaultEndHour,
          minute: endTimeData['minute'] ?? NotificationConstants.defaultEndMinute,
        );
      }
    } catch (e) {
      debugPrint('Error loading time range: $e');
    }
  }

  /// Save time range to storage
  Future<void> saveTimeRange(TimeOfDay startTime, TimeOfDay endTime) async {
    try {
      _startTime.value = startTime;
      _endTime.value = endTime;

      await _storage.write(NotificationConstants.startTimeKey, {
        'hour': startTime.hour,
        'minute': startTime.minute,
      });

      await _storage.write(NotificationConstants.endTimeKey, {
        'hour': endTime.hour,
        'minute': endTime.minute,
      });
    } catch (e) {
      debugPrint('Error saving time range: $e');
    }
  }

  /// Get list of dhikr data
  List<Dhikr> getDhikrList() {
    return [
      const Dhikr(subtitle: 'سبحان الله', sound: 'sou_tasbeeh'),
      const Dhikr(subtitle: 'الحمد لله', sound: 'sou_tahmeed'),
      const Dhikr(subtitle: 'الله أكبر', sound: 'sou_takbeer'),
      const Dhikr(subtitle: 'لا إله إلا الله', sound: 'sou_tahleel'),
      const Dhikr(subtitle: 'لا حول ولا قوة إلا بالله', sound: 'sou_hawqalah'),
      const Dhikr(subtitle: 'أستغفر الله', sound: 'sou_esteghfar'),
      const Dhikr(subtitle: 'سبحان الله وبحمده', sound: 'sou_tasbhamd'),
      const Dhikr(subtitle: 'سبحان الله العظيم', sound: 'sou_tasbta3zeem'),
      const Dhikr(subtitle: 'لا إله إلا الله وحده لا شريك له', sound: 'sou_tawheed'),
      const Dhikr(subtitle: 'اللهم صل على محمد', sound: 'sou_salah'),
      const Dhikr(subtitle: 'الباقيات الصالحات', sound: 'sou_baqyat'),
      const Dhikr(subtitle: 'حسبنا الله ونعم الوكيل', sound: 'sou_ghamm'),
      const Dhikr(subtitle: 'العدد', sound: 'sou_adadd'),
    ];
  }

  /// Get sound path for a specific phrase
  String _getSoundForPhrase(String phrase) {
    final dhikrs = getDhikrList();
    for (final dhikr in dhikrs) {
      if (phrase.contains(dhikr.subtitle)) {
        return dhikr.sound;
      }
    }
    return 'sou_tasbeeh'; // Default sound
  }

  /// Prepare dhikr notifications for the next 7 days
  List<NotificationData> prepareDhikrNotifications() {
    final List<NotificationData> notifications = [];
    final dhikrs = getDhikrList();
    final today = DateTime.now();

    // Loop through 7 days
    for (int day = 0; day < NotificationConstants.maxNotificationDays; day++) {
      final targetDate = today.add(Duration(days: day));

      // Loop through hours within the user's selected time range
      for (int hour = startTime.hour; hour <= endTime.hour; hour++) {
        // Use modulo to cycle through the dhikrs list
        final dhikrIndex = (hour - startTime.hour) % dhikrs.length;
        final dhikr = dhikrs[dhikrIndex];

        // Calculate the target time
        final targetTime = DateTime(
          targetDate.year,
          targetDate.month,
          targetDate.day,
          hour,
          startTime.minute,
        );

        // Only schedule future notifications
        if (targetTime.isAfter(DateTime.now())) {
          notifications.add(
            NotificationData(
              title: 'Audio Athkar'.tr,
              subtitle: dhikr.subtitle,
              time: targetTime,
              payload: NotificationConstants.soundNotificationsPayload,
              sound: dhikr.sound,
              channelKey: NotificationConstants.dhikrChannelKey,
              groupKey: NotificationConstants.dhikrGroupKey,
              id: NotificationConstants.dhikrBaseId + notifications.length,
            ),
          );
        }
      }
    }

    return notifications;
  }

  /// Schedule dhikr notifications in batches
  Future<void> scheduleNotificationsBatch(
    List<NotificationData> notifications,
    String defaultPayload,
  ) async {
    await _notificationService.scheduleNotificationsBatch(
      notifications,
      defaultPayload,
    );
  }

  /// Schedule all dhikr notifications
  Future<void> scheduleDhikrNotifications() async {
    try {
      await _notificationService.cancelOldNotificationsAndScheduleNewOnes(
        NotificationConstants.soundNotificationsPayload,
        () async {
          final notifications = prepareDhikrNotifications();
          await scheduleNotificationsBatch(
            notifications,
            NotificationConstants.soundNotificationsPayload,
          );
        },
      );
    } catch (e) {
      debugPrint('Error scheduling dhikr notifications: $e');
    }
  }

  /// Cancel dhikr notifications
  Future<void> cancelDhikrNotifications() async {
    await _notificationService.cancelNotificationsByPayload(
      NotificationConstants.soundNotificationsPayload,
    );
  }

  /// Check if dhikr notifications are scheduled
  Future<bool> hasDhikrNotifications() async {
    return await _notificationService.hasScheduledNotifications(
      NotificationConstants.soundNotificationsPayload,
    );
  }

  /// Get count of scheduled dhikr notifications
  Future<int> getDhikrNotificationCount() async {
    return await _notificationService.getNotificationCountByPayload(
      NotificationConstants.soundNotificationsPayload,
    );
  }

  /// Schedule a single notification
  Future<bool> scheduleNotification(NotificationData notification) async {
    return await _notificationService.scheduleNotification(notification);
  }

  /// Send immediate notification
  Future<bool> sendNotification(NotificationData notification) async {
    return await _notificationService.sendNotification(notification);
  }

  /// Cancel notification by ID
  Future<bool> cancelNotification(int id) async {
    return await _notificationService.cancelNotification(id);
  }

  /// Get all scheduled notifications
  Future<List<dynamic>> getAllScheduledNotifications() async {
    return await _notificationService.getScheduledNotifications();
  }
}
