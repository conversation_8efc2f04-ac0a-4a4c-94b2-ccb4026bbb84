
// Export all public APIs
export 'src/services/notification_service.dart';
export 'src/models/notification_models.dart';
export 'src/utils/notification_utility.dart';
export 'src/constants/notification_constants.dart';

import 'src/services/notification_service.dart';

/// Quick access to notification service for testing
class AwesomeNotificationsService {
  static NotificationService get instance => NotificationService.instance;

  /// Send a test notification
  static Future<bool> sendTestNotification({String? sound}) async {
    return await instance.sendTestNotification(sound: sound);
  }
}
