<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.salawati.awesome_notifications_service">

    <!-- Awesome Notifications Required Permissions -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application>
        <!-- Awesome Notifications Foreground Service -->
        <service android:name="me.carda.awesome_notifications.core.services.ForegroundService"
                 android:enabled="true"            
                 android:exported="false"
                 android:stopWithTask="true"
                 android:foregroundServiceType="specialUse">
            <property android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE" 
                     android:value="Notification service for prayer times and dhikr reminders"/>
        </service>
    </application>

</manifest>
