#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint awesome_notifications_service.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'awesome_notifications_service'
  s.version          = '1.0.0'
  s.summary          = 'A comprehensive notification service package using awesome_notifications for Salawati app.'
  s.description      = <<-DESC
A comprehensive notification service package using awesome_notifications for Salawati app.
                       DESC
  s.homepage         = 'https://github.com/salawati/awesome_notifications_service'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Salawati Team' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.platform = :ios, '12.0'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  s.swift_version = '5.0'
end
