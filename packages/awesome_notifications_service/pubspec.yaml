name: awesome_notifications_service
description: A comprehensive notification service package using awesome_notifications for Salawati app.
version: 1.0.0

environment:
  sdk: ">=2.19.0 <4.0.0"
  flutter: ">=2.5.0"

dependencies:
  flutter:
    sdk: flutter
  awesome_notifications: ^0.10.1
  timezone: ^0.10.1
  get: ^4.7.2
  get_storage: ^2.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  # Plugin configuration
  plugin:
    platforms:
      android:
        package: com.salawati.awesome_notifications_service
        pluginClass: AwesomeNotificationsServicePlugin
      ios:
        pluginClass: AwesomeNotificationsServicePlugin
