# Awesome Notifications Service - Configuration Guide

This document explains how the `awesome_notifications_service` package automatically configures all necessary permissions and settings for your Flutter app.

## 🔧 Automatic Configuration

This package is designed to be **completely self-contained**. When you add it to your project, it automatically configures all necessary permissions and settings without requiring any manual setup in your main app.

## 📱 Android Configuration

### Permissions Automatically Added

The package automatically adds these permissions to your app:

```xml
<!-- Core notification permissions -->
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

<!-- Advanced notification features -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
<uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
```

### Services Automatically Configured

The package automatically adds the awesome_notifications foreground service:

```xml
<service android:name="me.carda.awesome_notifications.core.services.ForegroundService"
         android:enabled="true"            
         android:exported="false"
         android:stopWithTask="true"
         android:foregroundServiceType="specialUse">
    <property android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE" 
             android:value="Notification service for prayer times and dhikr reminders"/>
</service>
```

### Build Requirements

- **Minimum SDK**: 23 (Android 6.0)
- **Target SDK**: 34 (Android 14)
- **Compile SDK**: 34+
- **Gradle**: 8.1.1+
- **Kotlin**: 1.7.10+

## 🍎 iOS Configuration

### Automatic Podfile Configuration

The package automatically configures the iOS Podfile with awesome_notifications requirements:

```ruby
# Minimum iOS deployment target
platform :ios, '12.0'

# Awesome Notifications pod modifications are automatically applied
```

### Capabilities Automatically Added

- Notification permissions
- Background app refresh
- Background processing
- Critical alerts (for prayer times)

### Build Requirements

- **Minimum iOS**: 12.0
- **Xcode**: 14.0+
- **Swift**: 5.0+

## 🚀 How It Works

### 1. Plugin Architecture

The package uses Flutter's plugin architecture to automatically merge its configuration with your main app:

```yaml
# In packages/awesome_notifications_service/pubspec.yaml
flutter:
  plugin:
    platforms:
      android:
        package: com.salawati.awesome_notifications_service
        pluginClass: AwesomeNotificationsServicePlugin
      ios:
        pluginClass: AwesomeNotificationsServicePlugin
```

### 2. Manifest Merging (Android)

Flutter automatically merges the package's `AndroidManifest.xml` with your app's manifest, adding all necessary permissions and services.

### 3. Podspec Integration (iOS)

The package's `.podspec` file automatically configures iOS build settings and dependencies.

## 🔍 Verification

### Check Android Configuration

To verify the configuration was applied correctly:

1. Build your app: `flutter build apk`
2. Check the generated manifest: `android/app/build/intermediates/merged_manifests/`
3. Verify all permissions are present

### Check iOS Configuration

To verify the iOS configuration:

1. Run: `cd ios && pod install`
2. Check that awesome_notifications pod is installed
3. Verify minimum deployment target is iOS 12.0

## 🛠 Troubleshooting

### Common Issues

#### Android Build Errors

If you encounter build errors:

1. **Clean and rebuild**:
   ```bash
   flutter clean
   flutter pub get
   flutter build apk
   ```

2. **Check Gradle version**: Ensure you're using Gradle 8.1.1+

3. **Verify SDK versions**: Check that your `android/app/build.gradle` has:
   ```gradle
   android {
       compileSdkVersion 34
       defaultConfig {
           minSdkVersion 23
           targetSdkVersion 34
       }
   }
   ```

#### iOS Build Errors

If you encounter iOS build errors:

1. **Clean pods**:
   ```bash
   cd ios
   rm -rf Pods Podfile.lock
   pod install
   ```

2. **Check deployment target**: Ensure your iOS deployment target is 12.0+

3. **Verify Xcode version**: Use Xcode 14.0 or later

### Permission Issues

If notifications aren't working:

1. **Check runtime permissions**:
   ```dart
   bool isAllowed = await AwesomeNotificationsService.isNotificationAllowed();
   if (!isAllowed) {
     await AwesomeNotificationsService.requestPermissions();
   }
   ```

2. **Check device settings**: Ensure notifications are enabled in device settings

3. **Check battery optimization**: Disable battery optimization for your app

## 📋 Manual Override (Not Recommended)

If you need to manually override any configuration, you can do so in your main app's configuration files. However, this is **not recommended** as it defeats the purpose of the self-contained package.

### Android Manual Override

Add to your main `android/app/src/main/AndroidManifest.xml`:

```xml
<!-- Only if you need to override package settings -->
<uses-permission android:name="android.permission.CUSTOM_PERMISSION" />
```

### iOS Manual Override

Modify your main `ios/Podfile`:

```ruby
# Only if you need to override package settings
post_install do |installer|
  # Your custom configurations
end
```

## 🔄 Updates

When updating the package:

1. **Update pubspec.yaml**:
   ```yaml
   dependencies:
     awesome_notifications_service:
       path: packages/awesome_notifications_service
   ```

2. **Clean and rebuild**:
   ```bash
   flutter clean
   flutter pub get
   ```

3. **For iOS, update pods**:
   ```bash
   cd ios && pod install
   ```

## 📞 Support

If you encounter any configuration issues:

1. Check this configuration guide
2. Review the main README.md
3. Contact the development team

## ✅ Configuration Checklist

Use this checklist to verify your setup:

### Android
- [ ] Package added to pubspec.yaml
- [ ] `flutter pub get` completed successfully
- [ ] App builds without errors
- [ ] Permissions appear in generated manifest
- [ ] Notifications work on device

### iOS
- [ ] Package added to pubspec.yaml
- [ ] `pod install` completed successfully
- [ ] App builds without errors
- [ ] Minimum deployment target is iOS 12.0+
- [ ] Notifications work on device

### Both Platforms
- [ ] Notification permissions granted
- [ ] Scheduled notifications work
- [ ] Custom sounds play correctly
- [ ] App survives background/foreground cycles
