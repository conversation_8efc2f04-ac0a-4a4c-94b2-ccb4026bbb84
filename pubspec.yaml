name: salawati
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.

# android version
# version: 1.0.4+13

# ios version
version: 1.0.26+33

environment:
  sdk: ">=3.5.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  flutter_svg: ^2.1.0
  lottie: ^3.1.1
  flutter_screenutil: ^5.9.3
  dio: ^5.8.0+1
  intl: ^0.19.0
  cupertino_icons: ^1.0.8
  get: ^4.7.2
  get_storage: ^2.1.1
  carousel_slider: ^5.0.0
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  flutter_bloc: ^9.1.0
  sliding_up_panel2: ^3.3.0+1
  auto_size_text: ^3.0.0
  geolocator: ^14.0.0
  geocoding: ^3.0.0
  flutter_compass: ^0.8.1
  permission_handler: ^12.0.0+1
  path_provider: ^2.1.5
  lat_lng_to_timezone: ^0.2.0
  adhan: ^2.0.0+1
  awesome_notifications: ^0.10.1
  timezone: ^0.10.1
  # nominatim_flutter: ^0.0.7
  url_launcher: ^6.3.1
  just_audio: ^0.10.1
  dropdown_button2: ^2.3.9
  animated_custom_dropdown: ^3.1.1
  share_plus: ^11.0.0
  syncfusion_flutter_charts: ^29.1.40
  firebase_auth: ^5.5.3
  firebase_core: ^3.13.0
  google_sign_in: ^6.3.0
  firebase_messaging: ^15.2.5
  firebase_crashlytics: ^4.3.5
  # device_info_plus: ^11.4.0
  google_fonts: ^6.2.1
  google_maps_flutter: ^2.12.1
  google_maps_flutter_android: ^2.16.1
  flutter_polyline_points: ^2.1.0
  expandable_page_view: ^1.0.17
  hijri: ^3.0.0
  syncfusion_flutter_core: ^29.1.40
  home_widget: ^0.7.0+1
  # workmanager: ^0.5.2
  dotted_line: ^3.2.3
  sign_in_with_apple: ^7.0.1
  flutter_localizations:
    sdk: flutter
  syncfusion_localizations: ^29.1.40
  syncfusion_flutter_datepicker: ^29.1.40
  flutter_timezone: ^4.1.0
  horizontal_list_view: ^1.1.0
  vibration: ^3.1.3

  mobile_device_identifier: ^0.0.3

  flutter_animate: ^4.5.2 # alarm: ^4.0.8
  connectivity_plus: ^6.1.4
  workmanager:
    git:
      url: https://github.com/fluttercommunity/flutter_workmanager.git
      path: workmanager
      ref: main
  compassx: ^1.0.1
  path: ^1.9.1
  sqflite: ^2.4.2
  disable_battery_optimization:
    git:
      url: https://github.com/pvsvamsi/Disable-Battery-Optimizations
      ref: 3bd744d03c416859bbaedecd61c1c11fc022ed75
  #for new backgroundservices
  # flutter_background_service: ^5.0.10
  screenshot: ^3.0.0
  path_provider_foundation: ^2.4.1
  shared_preferences: ^2.5.3
  app_settings: ^6.1.1
  instagram_page_indicator: ^0.1.1
  logger: ^2.5.0
  image: ^4.5.4
  sensors_plus: ^6.1.1
  win32: ^5.12.0
  rive_native: ^0.0.1-dev.8
  rxdart: ^0.28.0
  hijri_calendar: ^1.0.7+7
  location: ^8.0.0
  awesome_notifications_service:
    path: packages/awesome_notifications_service

dev_dependencies:
  flutter_test:
    sdk: flutter
  launcher_name: ^1.0.2
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

launcher_name:
  default: "Salawati"
  ar: "صلواتي"
  fr: "Salawati"
  ur: "صلواتی"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/json/
    - assets/svgs/
    - assets/images/
    - assets/launcher/
    # - assets/sounds/full_athan.wav
    # - assets/sounds/short_athan.wav
    - assets/sounds/
    - assets/gif/
    - assets/dbs/
    - assets/images/share_background/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    #   - family: MyFont
    #     fonts:
    #       - asset: assets/font/Tajawal-Regular.ttf
    - family: adwaa-alsalaf
      fonts:
        - asset: "assets/fonts/adwaa-alsalaf/adwaa-alsalaf-regular.ttf"
        - asset: "assets/fonts/adwaa-alsalaf/adwaa-alsalaf-bold.ttf"
    - family: Amiri
      fonts:
        - asset: "assets/fonts/amiri/amiri.ttf"
    - family: Hafs
      fonts:
        - asset: "assets/fonts/hafs/hafs.ttf"
    - family: Tajawal
      fonts:
        # Define each font file and its corresponding weight.
        # Weights typically range from 100 (Thin) to 900 (Black).
        - asset: assets/fonts/tajawal/Tajawal-ExtraLight.ttf
          weight: 200 # ExtraLight weight
        - asset: assets/fonts/tajawal/Tajawal-Light.ttf
          weight: 300 # Light weight
        - asset: assets/fonts/tajawal/Tajawal-Regular.ttf
          weight: 400 # Regular (normal) weight
          # style: normal # 'style: normal' is default, so usually omitted
        - asset: assets/fonts/tajawal/Tajawal-Medium.ttf
          weight: 500 # Medium weight
        - asset: assets/fonts/tajawal/Tajawal-Bold.ttf
          weight: 700 # Bold weight
        - asset: assets/fonts/tajawal/Tajawal-ExtraBold.ttf
          weight: 800 # ExtraBold weight
        - asset: assets/fonts/tajawal/Tajawal-Black.ttf
          weight: 900 # Black weight
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


