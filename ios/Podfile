# Uncomment this line to define a global platform for your project
platform :ios, '14.0'

# Ensure all pods use at least iOS 12.0 (required by awesome_notifications)

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!
  pod 'SwiftyJSON', '~> 4.0'

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end

target 'salah_widgetExtension' do
  use_frameworks!
  use_modular_headers!
  inherit! :search_paths
  pod 'SwiftyJSON', '~> 4.0'

    pod 'home_widget', :path => '.symlinks/plugins/home_widget/ios'

  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    # 1) keep Flutter’s additional iOS build settings
    flutter_additional_ios_build_settings(target)

    # 2) add your GCC_PREPROCESSOR_DEFINITIONS
    target.build_configurations.each do |config|
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        'AUDIO_SESSION_MICROPHONE=0',
        'PERMISSION_NOTIFICATIONS=1'
      ]

      # Ensure all pods use at least iOS 12.0 (required by awesome_notifications)
      if config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'].to_f < 12.0
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      end
    end

    # 3) look for the Rive Native setup script and prepend Dart SDK to PATH
    target.build_phases.each do |phase|
      if phase.respond_to?(:shell_script) && phase.shell_script.include?("dart run rive_native:setup")
        phase.shell_script = <<~SH
          export PATH="$PATH:#{ENV['FLUTTER_ROOT']}/bin/cache/dart-sdk/bin"
          export PATH="$PATH:#{ENV['FLUTTER_ROOT']}/bin"
          #{phase.shell_script}
        SH
      end
    end
  end

  # 4) run Flutter’s post-install hook if present
  flutter_post_install(installer) if defined?(flutter_post_install)

  ################  Awesome Notifications pod modification 1 ###################
  awesome_pod_file = File.expand_path(File.join('plugins', 'awesome_notifications', 'ios', 'Scripts', 'AwesomePodFile'), '.symlinks')
  require awesome_pod_file
  update_awesome_pod_build_settings(installer)
  ################  Awesome Notifications pod modification 1 ###################
end

################  Awesome Notifications pod modification 2 ###################
awesome_pod_file = File.expand_path(File.join('plugins', 'awesome_notifications', 'ios', 'Scripts', 'AwesomePodFile'), '.symlinks')
require awesome_pod_file
update_awesome_main_target_settings('Runner', File.dirname(File.realpath(__FILE__)), flutter_root)
################  Awesome Notifications pod modification 2 ###################
