import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/permissions/presentation/controller/permission_flow_controller.dart';
import 'package:salawati/features/permissions/presentation/widgets/permission_card.dart';

class PermissionFlowScreen extends StatelessWidget {
  const PermissionFlowScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PermissionFlowController>(
      init: PermissionFlowController(),
      builder: (controller) {
        return Scaffold(
          body: SafeArea(
            child: Stack(
              children: [
                // Background
                Image.asset(
                  AppImages.kMainbg,
                  height: double.infinity,
                  width: double.infinity,
                  fit: BoxFit.fill,
                ),

                // Content
                Column(
                  children: [
                    // Header
                    _buildHeader(controller),

                    // Progress indicator
                    _buildProgressIndicator(controller),

                    32.verticalSpace,

                    // Permission card
                    Expanded(
                      child: Center(
                        child: SingleChildScrollView(
                          child: Obx(() => PermissionCard(
                            icon: controller.getStepIcon(controller.currentStep),
                            title: controller.getStepTitle(controller.currentStep),
                            description: controller.getStepDescription(controller.currentStep),
                            buttonText: controller.getStepButtonText(controller.currentStep),
                            onAllow: controller.requestCurrentPermission,
                            onSkip: _shouldShowSkip(controller.currentStep)
                                ? controller.skipCurrentPermission
                                : null,
                            isLoading: controller.isLoading,
                            errorMessage: controller.errorMessage.isEmpty
                                ? null
                                : controller.errorMessage,
                            onClearError: controller.clearError,
                            canSkip: _shouldShowSkip(controller.currentStep),
                          )),
                        ),
                      ),
                    ),

                    // Skip all button (only show if not on complete step)
                    if (controller.currentStep != PermissionStep.complete)
                      _buildSkipAllButton(controller),

                    24.verticalSpace,
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(PermissionFlowController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          // Back button (only show after first step)
          SizedBox(
            width: 40.w,
            height: 40.w,
            child: controller.currentStep == PermissionStep.location
                ? Container() // Empty for first step
                : GestureDetector(
                    onTap: () => _handleBackButton(controller),
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColor.kRectangleColor.withOpacity(0.8),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.arrow_back,
                        color: AppColor.kGreyColor,
                        size: 20.sp,
                      ),
                    ),
                  ),
          ),

          Expanded(
            child: CustomText(
              _getHeaderTitle(controller.currentStep),
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppColor.kOrangeColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Close button (only show if not on complete step)
          SizedBox(
            width: 40.w,
            height: 40.w,
            child: controller.currentStep == PermissionStep.complete
                ? Container() // Empty for complete step
                : GestureDetector(
                    onTap: () => _showSkipDialog(controller),
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColor.kRectangleColor.withOpacity(0.8),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        color: AppColor.kGreyColor,
                        size: 20.sp,
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(PermissionFlowController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          // Progress bar
          Obx(() {
            final progress = controller.getProgress();
            return Container(
              height: 4.h,
              decoration: BoxDecoration(
                color: AppColor.kGreyColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progress,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColor.kOrangeColor,
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
              ),
            );
          }),

          8.verticalSpace,

          // Step indicator
          Obx(() => CustomText(
            _getStepText(controller.currentStep),
            style: TextStyle(
              fontSize: 14.sp,
              color: controller.currentStep == PermissionStep.complete
                  ? AppColor.kOrangeColor
                  : AppColor.kGreyColor,
              fontWeight: controller.currentStep == PermissionStep.complete
                  ? FontWeight.w600
                  : FontWeight.normal,
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildSkipAllButton(PermissionFlowController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: TextButton(
        onPressed: () => _showSkipDialog(controller),
        child: CustomText(
          'skip_all_permissions'.tr,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.kGreyColor,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    );
  }

  String _getHeaderTitle(PermissionStep step) {
    switch (step) {
      case PermissionStep.complete:
        return 'setup_complete'.tr;
      default:
        return 'setup_permissions'.tr;
    }
  }

  String _getStepText(PermissionStep step) {
    switch (step) {
      case PermissionStep.location:
        return 'step_1_of_3'.tr;
      case PermissionStep.notification:
        return 'step_2_of_3'.tr;
      case PermissionStep.batteryOptimization:
        return 'step_3_of_3'.tr;
      case PermissionStep.complete:
        return 'completed'.tr;
    }
  }

  bool _shouldShowSkip(PermissionStep step) {
    return step != PermissionStep.complete;
  }

  void _handleBackButton(PermissionFlowController controller) {
    // For now, just go back in navigation
    // In a more complex implementation, you might want to go to previous step
    Get.back();
  }

  void _showSkipDialog(PermissionFlowController controller) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: CustomText(
          'skip_permissions_title'.tr,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: CustomText(
          'skip_permissions_message'.tr,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.kGreyColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: CustomText(
              'Cancel',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.kGreyColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.skipAllPermissions();
            },
            child: CustomText(
              'Skip All',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.kOrangeColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
