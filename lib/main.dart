import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:home_widget/home_widget.dart' hide callbackDispatcher;
import 'package:rive_native/rive_native.dart';
import 'package:salawati/app_binings.dart';
import 'package:salawati/core/data/providers/salawati_database_provider.dart';
import 'package:salawati/core/services/permission_manager.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/app_locale.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:salawati/features/home_widget/prayer_times_home_widget.dart';
import 'package:salawati/features/ibadah/presentation/cubit/ibadah_cubit.dart';
import 'package:salawati/features/location/data/providers/locations_database_provider.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/prayer/presentation/widgets/new_widget.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';
import 'package:salawati/firebase_options.dart';
import 'package:workmanager/workmanager.dart';

import 'core/utils/app_theme.dart';
import 'features/mosque/data/repo/cities_repo.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';

final RouteObserver<ModalRoute<void>> routeObserver =
    RouteObserver<ModalRoute<void>>();

@pragma('vm:entry-point')
void callbackDispatcher2() {
  Workmanager().executeTask((task, inputData) async {
    try {
      debugPrint("🔵 [Background] Starting widget update task: $task");

      await GetStorage.init();
      await AppFunctions.initializeDateLocales();

      if (Platform.isIOS) {
        await _updateIOSWidget();
      } else if (Platform.isAndroid) {
        await _updateAndroidWidget();
      }

      debugPrint("🔵 [Background] Widget updated successfully");
      return Future.value(true);
    } catch (e, stack) {
      debugPrint("🔴 [Background Error] $e");
      debugPrint("🔴 [Stack] $stack");
      return Future.value(false);
    }
  });
}

Future<void> _updateIOSWidget() async {
  // switch (task) {
  // case iOSBackgroundAppRefresh:
  debugPrint('widget start callbackDispatcher');
  // await initializeEssentials();
  // await initializeApp();

  AppBindings().dependencies();

  // Get.put<LocationController>(LocationController(), permanent: true);
  // Get.put<PrayerController>(PrayerController(), permanent: true);
  // Get.put<SettingsController>(
  //   SettingsController(prayerController: Get.find()),
  //   permanent: true,
  // );

  await updateNewWidget();

  debugPrint('amin103');
  // Get.put(PrayerDataService());

  //     break;
  //   default:
  //     return Future.value(false);
  // }
}

Future<void> _updateAndroidWidget() async {
  AppBindings().dependencies();

  // Directly update widget without post-frame callback
  await PrayerWidgetService.updatePrayerTimeWidget(
      Get.find<PrayerController>());
  debugPrint("🔵 [Background] Widget updated successfully");
}

const taskIdinfier = "salawati.refreshWidget2";

const iOSBackgroundAppRefresh = "salawati.iOSBackgroundAppRefresh2";

const widgetappGroup = 'group.salawati_prayer_time';

Future<void> updateNewWidget() async {
  if (Get.isRegistered<PrayerDataService>()) {
    try {
      await PrayerDataService.instance.refreshPrayerData();
      await PrayerDataService.instance.sendToWidget();
      debugPrint('Widget, is Synced in background on callbackDispatcher ');
    } catch (e) {
      debugPrint('Widget on callbackDispatcher group amin $e');
    }
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Awesome Notifications FIRST through the service package
  await NotificationService.initializeEarly();

  await RiveNative.init();
  await LocationsDatabaseProvider.init();

  // Cancel existing tasks before registering new ones

  // Use a single callback dispatcher for both platforms
  await Workmanager().initialize(callbackDispatcher2, isInDebugMode: false);

  if (Platform.isIOS) {
    await HomeWidget.setAppGroupId(widgetappGroup);
  }

  await initializeApp();

  runApp(const MyApp());
}

Future<void> initializeApp() async {
  try {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    await firebaseCrashlyticsInitializer();

    await initializeEssentials();
    await setupDatabase();
    await setupPushNotifications();
  } catch (e) {
    await FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
  }
}

Future<void> initializeEssentials() async {
  await GetStorage.init();
  await AppFunctions.initializeDateLocales();
  await CitiesRepo.init();
  await ScreenUtil.ensureScreenSize();
  AppFunctions.preventRotateScreen();
  Get.put<LocationService>(LocationService());
  Get.put<PermissionManager>(PermissionManager());
}

Future<void> setupPushNotifications() async {
  final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
  debugPrint("APNS Token: $apnsToken");
}

Future<void> setupDatabase() async {
  await SalawatiDatabaseProvider.init();
}

Future<void> firebaseCrashlyticsInitializer() async {
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    if (Platform.isIOS) {
      _startWorkmanager();
    } else {
      _startWorkmanager2();
    }
  }

  void _startWorkmanager() async {
    debugPrint('amin99');
    await Workmanager().registerPeriodicTask(
      iOSBackgroundAppRefresh,
      iOSBackgroundAppRefresh,
      // constraints: Constraints(
      //   networkType: NetworkType.not_required,
      //   requiresCharging: false,
      //   requiresStorageNotLow: false,
      //   requiresBatteryNotLow: false,
      //   requiresDeviceIdle: false,
      // ),
      frequency: const Duration(minutes: 20),
      initialDelay: const Duration(seconds: 2),
    );
    await updateNewWidget();
  }

  void _startWorkmanager2() async {
    debugPrint('worker _startWorkmanager2 Starting Android background tasks');
    await Workmanager().registerPeriodicTask(
      'salati_android_widget_update2',
      'salati_android_widget_update2',
      frequency: const Duration(minutes: 15),
      existingWorkPolicy: ExistingWorkPolicy.replace,
    );
    await Workmanager().registerOneOffTask(
      'salati_android_widget_update3',
      'salati_android_widget_update3',
      initialDelay: const Duration(seconds: 3),
      existingWorkPolicy: ExistingWorkPolicy.replace,
    );

    await updateNewWidget();
  }

  // Future<void> logNotificationChannels() async {
  @override
  Widget build(BuildContext context) {
    Get.put(AppTheme());

    HomeWidget.saveWidgetData<double>(
        'device_pixel_ratio', MediaQuery.of(context).devicePixelRatio);
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, s) {
        return MultiBlocProvider(
          providers: [
            BlocProvider<AuthCubit>(create: (context) => AuthCubit()),
            BlocProvider<QuranCubit>(
                create: (context) => QuranCubit()..getReciters()),
            BlocProvider<IbadahCubit>(
                create: (context) => IbadahCubit()..getUserIbadat()),
          ],
          child: GetMaterialApp(
            navigatorObservers: [routeObserver],
            // showPerformanceOverlay: true,
            // localizationsDelegates: [
            //   GlobalMaterialLocalizations.delegate,
            //   GlobalWidgetsLocalizations.delegate,
            //   GlobalCupertinoLocalizations.delegate,
            //   SfGlobalLocalizations.delegate
            // ],
            // supportedLocales: [
            //   Locale('ar'),
            //   Locale('en'),
            //   Locale('fr'),
            //   Locale('ur'),
            // ],
            initialBinding: AppBindings(),
            builder: (context, child) {
              // Access theme through instance
              final appTheme = AppTheme.instance;
              return Theme(
                data: appTheme.theme,
                child: MediaQuery(
                  data: MediaQuery.of(context).copyWith(
                    textScaler: const TextScaler.linear(1.0),
                  ),
                  child: child!,
                ),
              );
            },
            locale: Locale(cacheMemory.read('lang') ?? 'ar'),
            translations: AppLocale(),
            theme: Get.find<AppTheme>().theme,
            debugShowCheckedModeBanner: false,
            getPages: AppRouter.pages,
            initialRoute: AppRouter.kSplashScreen,
          ),
        );
      },
    );
  }
}
