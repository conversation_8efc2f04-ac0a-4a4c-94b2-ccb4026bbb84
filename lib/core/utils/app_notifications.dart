import 'dart:async';
import 'dart:convert';

import 'package:app_settings/app_settings.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart' as firebase;
import 'package:flutter/material.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_battery_optimiztion.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class AppNotifications {
  static final AppNotifications _instance = AppNotifications._internal();
  static final NotificationService _notificationService = NotificationService.instance;

  factory AppNotifications() => _instance;

  AppNotifications._internal();

  static Future<void> configureLocalTimeZone() async {
    tz.initializeTimeZones();
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));
  }

  static Future<void> testAlarm2() async {
    await configureLocalTimeZone();

    var sound = getAdhanSoundPath(AthanSoundType.values[1], false);
    var androidSound = sound?.substring(0, sound.indexOf("."));

    final notification = NotificationData(
      id: 0,
      title: 'scheduled title',
      subtitle: 'scheduled body',
      time: DateTime.now().add(const Duration(seconds: 5)),
      sound: androidSound != null ? 'resource://raw/$androidSound' : null,
      channelKey: NotificationConstants.athanChannelKey,
      groupKey: NotificationConstants.athanGroupKey,
      payload: NotificationConstants.athanNotificationsPayload,
    );

    await _notificationService.scheduleNotification(notification);
  }

  static Future<void> initLocaleNotification() async {
    // Initialize the awesome notifications service
    final isInitialized = await _notificationService.initialize();

    if (!isInitialized) {
      debugPrint('❌ Failed to initialize notification service');
      return;
    }

    debugPrint('✅ Notification service initialized successfully');

    // Add a delay before requesting permissions
    await Future.delayed(const Duration(seconds: 1));

    // Check and request notification permissions
    await checkAndRequestNotificationPermissions();

    await AppBatteryOptimization.initAutoStart();

    debugPrint('✅ Notification setup completed');
  }

  static Future<bool> checkAndRequestNotificationPermissions() async {
    try {
      // Use the new notification service for permission handling
      final permissionStatus = await _notificationService.requestPermissions();

      if (permissionStatus.hasAllPermissions) {
        debugPrint('Notification permissions already granted');
        return true;
      }

      // Show custom dialog if denied
      await _showNotificationPermissionDialog();
      return false;
    } catch (e) {
      debugPrint('Error checking permissions: $e');
      return false;
    }
  }

  static Future<void> _showNotificationPermissionDialog() async {
    await Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        title: CustomText('enable_notifications'.tr),
        content: CustomText(
          'notification_settings_request'.tr,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: CustomText('Cancel'.tr),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              await AppSettings.openAppSettings(
                  type: AppSettingsType.notification);
            },
            child: CustomText('openSettings'.tr),
          ),
        ],
      ),
      barrierDismissible: false, // Force user interaction
    );
  }

  static Future<void> sendScheduledNotification({
    int? id,
    required String title,
    required String subtitle,
    required DateTime time,
    String? sound,
    required String payload,
  }) async {
    await configureLocalTimeZone();

    time = DateTime.parse(
      time.toIso8601String().replaceFirst("z", "").replaceFirst("Z", ''),
    );

    if (tz.TZDateTime.from(time, tz.local)
        .isBefore(tz.TZDateTime.now(tz.local))) {
      return;
    }

    var androidSound = sound?.substring(0, sound.indexOf(".")) ?? "";

    // Determine channel key based on payload
    String channelKey = NotificationConstants.generalChannelKey;
    String groupKey = 'general_group';

    if (payload.contains(NotificationConstants.athanNotificationsPayload)) {
      channelKey = NotificationConstants.athanChannelKey;
      groupKey = NotificationConstants.athanGroupKey;
    } else if (payload.contains(NotificationConstants.soundNotificationsPayload)) {
      channelKey = NotificationConstants.dhikrChannelKey;
      groupKey = NotificationConstants.dhikrGroupKey;
    }

    // For awesome_notifications, we need to remove the file extension
    String? processedSound;
    if (sound != null) {
      processedSound = androidSound.replaceAll('.wav', '').replaceAll('.mp3', '');
      debugPrint('🔊 Original sound: $sound');
      debugPrint('🔊 Android sound: $androidSound');
      debugPrint('🔊 Processed sound: $processedSound');
    }

    final notification = NotificationData(
      id: id ?? DateTime.now().millisecondsSinceEpoch % 1000 + 1,
      title: title,
      subtitle: subtitle,
      time: time,
      sound: processedSound, // Use processed sound without extension
      payload: payload,
      channelKey: channelKey,
      groupKey: groupKey,
    );

    await _notificationService.scheduleNotification(notification);
  }

  // static Future<void> showNotifications(
  //   String? sound,
  //   List<String> messages,
  //   int id,
  //   String title,
  // ) async {
  //   const int maxPendingNotifications = 100; // Adjust as needed
  //   const int repeatIntervalInMinutes = 1; // Adjust as needed

  //   for (int i = 0; i < maxPendingNotifications; i++) {
  //     final messageIndex = i % messages.length;
  //     final message = messages[messageIndex];
  //     var androidSound = sound?.substring(0, sound.indexOf(".")) ?? "";

  //     await flutterLocalNotificationsPlugin.zonedSchedule(
  //       id + i,
  //       title,
  //       message,
  //       tz.TZDateTime.now(tz.local)
  //           .add(Duration(minutes: i * repeatIntervalInMinutes)),
  //       NotificationDetails(
  //         android: AndroidNotificationDetails(
  //           'main_channel',
  //           'Main Channel',
  //           channelDescription: 'ashwin',
  //           importance: Importance.max,
  //           priority: Priority.max,
  //           playSound: true,
  //           sound: sound == null
  //               ? null
  //               : RawResourceAndroidNotificationSound(androidSound),
  //           styleInformation: BigTextStyleInformation(message),
  //         ),
  //         iOS: const DarwinNotificationDetails(
  //           sound: 'default.wav',
  //           presentAlert: true,
  //           presentBadge: true,
  //           presentSound: true,
  //         ),
  //       ),
  //       // uiLocalNotificationDateInterpretation:
  //       //     UILocalNotificationDateInterpretation.absoluteTime,
  //       androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
  //       payload: jsonEncode({'message_index': messageIndex}),
  //     );
  //   }
  // }

  static Future<void> onSelectNotification(String? payload) async {
    if (payload != null) {
      final Map<String, dynamic> payloadMap = jsonDecode(payload);
      final messageIndex = payloadMap['message_index'];
      // Handle the selected notification
      debugPrint('Selected notification with message index: $messageIndex');
    }
  }

  static Future<void> cancelAllAthanNotifications() async {
    debugPrint('amin cancelAllAthanNotifications');
    await _notificationService.cancelAllNotifications();
  }

  static Future<void> cancelOldNotificationsAndScheduleNewOnes(
    String payload,
    Future<void> Function() scheduleNewNotifications,
  ) async {
    try {
      // Use the new notification service method
      await _notificationService.cancelOldNotificationsAndScheduleNewOnes(
        payload,
        scheduleNewNotifications,
      );
    } catch (e) {
      // Handle errors gracefully
      debugPrint('Error in cancelOldNotificationsAndScheduleNewOnes: $e');
    }
  }

  static Future<void> cancelNotificationsByPayload(String payload) async {
    await _notificationService.cancelNotificationsByPayload(payload);
  }

  static Future<bool> hasScheduledNotifications(String payload) async {
    return await _notificationService.hasScheduledNotifications(payload);
  }

  static void firebaseNotificationSetup() {
    firebase.FirebaseMessaging.onBackgroundMessage(
      firebaseMessagingBackgroundHandler,
    );
    firebase.FirebaseMessaging.onMessage
        .listen((firebase.RemoteMessage message) {
      AppFunctions.customSnackbar(
          message.notification!.body!, message.notification!.title!);
    });
    firebase.FirebaseMessaging.onMessageOpenedApp
        .listen((firebase.RemoteMessage message) {
      AppFunctions.customSnackbar(
          message.notification!.body!, message.notification!.title!);
    });
  }
}

enum AthanSoundType {
  athan1,
  athan2,
  athan3,
  athan4,
  silent,
}

String getAdhanName(AthanSoundType type) {
  switch (type) {
    case AthanSoundType.silent:
      return 'Use Default (Silent)'.tr;
    case AthanSoundType.athan1:
      return 'Athan 1'.tr;
    case AthanSoundType.athan2:
      return 'Athan 2'.tr;
    case AthanSoundType.athan3:
      return 'Athan 3'.tr;
    case AthanSoundType.athan4:
      return 'Athan 4'.tr;
  }
}

String? getAdhanSoundPath(AthanSoundType type, bool isFullAdhan,
    [bool fromAsset = false]) {
  if (type == AthanSoundType.silent) return null;
  if (fromAsset) {
    switch (type) {
      case AthanSoundType.athan4:
        return AppSounds.kAssetAthan4Short;
      case AthanSoundType.athan1:
        return AppSounds.kAssetAthan1Short;
      case AthanSoundType.athan2:
        return AppSounds.kAssetAthan2Short;
      case AthanSoundType.athan3:
        return AppSounds.kAssetAthan3Short;
      case AthanSoundType.silent:
        return null;
    }
  } else if (isFullAdhan) {
    switch (type) {
      case AthanSoundType.athan4:
        return AppSounds.kAthan4;
      case AthanSoundType.athan1:
        return AppSounds.kAthan1;
      case AthanSoundType.athan2:
        return AppSounds.kAthan2;
      case AthanSoundType.athan3:
        return AppSounds.kAthan3;
      case AthanSoundType.silent:
        return null;
    }
  } else {
    switch (type) {
      case AthanSoundType.athan4:
        return AppSounds.kAthan4Short;
      case AthanSoundType.athan1:
        return AppSounds.kAthan1Short;
      case AthanSoundType.athan2:
        return AppSounds.kAthan2Short;
      case AthanSoundType.athan3:
        return AppSounds.kAthan3Short;
      case AthanSoundType.silent:
        return null;
    }
  }
}

Future<void> firebaseMessagingBackgroundHandler(
    firebase.RemoteMessage remoteMessage) async {}
