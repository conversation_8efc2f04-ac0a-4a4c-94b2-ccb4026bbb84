import 'package:flutter/material.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'core/utils/app_notifications.dart';

/// Debug screen to test notification functionality
/// Add this to your app temporarily to test notifications
class DebugNotificationScreen extends StatefulWidget {
  const DebugNotificationScreen({super.key});

  @override
  State<DebugNotificationScreen> createState() => _DebugNotificationScreenState();
}

class _DebugNotificationScreenState extends State<DebugNotificationScreen> {
  String _status = 'Ready to test';
  bool _isLoading = false;

  void _updateStatus(String status) {
    setState(() {
      _status = status;
    });
  }

  Future<void> _runFullTest() async {
    setState(() {
      _isLoading = true;
      _status = 'Running full test...';
    });

    try {
      await AppNotifications.testNotificationSystem();
      _updateStatus('✅ Full test completed - check console logs');
    } catch (e) {
      _updateStatus('❌ Test failed: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testImmediateNotification() async {
    setState(() {
      _isLoading = true;
      _status = 'Sending immediate notification...';
    });

    try {
      final result = await NotificationService.instance.sendTestNotification(
        sound: 'athan1_short',
      );
      _updateStatus(result ? '✅ Immediate notification sent' : '❌ Failed to send');
    } catch (e) {
      _updateStatus('❌ Error: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testScheduledNotification() async {
    setState(() {
      _isLoading = true;
      _status = 'Scheduling notification for 5 seconds...';
    });

    try {
      await AppNotifications.sendScheduledNotification(
        id: 12345,
        title: 'Test Scheduled',
        subtitle: 'This notification was scheduled 5 seconds ago',
        time: DateTime.now().add(const Duration(seconds: 5)),
        sound: 'athan1_short',
        payload: 'TEST_SCHEDULED',
      );
      _updateStatus('✅ Notification scheduled for 5 seconds');
    } catch (e) {
      _updateStatus('❌ Error: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _checkPermissions() async {
    setState(() {
      _isLoading = true;
      _status = 'Checking permissions...';
    });

    try {
      final service = NotificationService.instance;
      final isAllowed = await service.areNotificationsAllowed();

      if (isAllowed) {
        _updateStatus('✅ Notifications are allowed');
      } else {
        _updateStatus('❌ Notifications not allowed - requesting...');
        final permissionStatus = await service.requestPermissions();
        _updateStatus(permissionStatus.isGranted
          ? '✅ Permissions granted'
          : '❌ Permissions denied');
      }
    } catch (e) {
      _updateStatus('❌ Error: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Debug'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Status: $_status',
                style: const TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 20),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              ElevatedButton(
                onPressed: _runFullTest,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.all(16),
                ),
                child: const Text(
                  'Run Full Test',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              const SizedBox(height: 12),

              ElevatedButton(
                onPressed: _checkPermissions,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: const EdgeInsets.all(16),
                ),
                child: const Text(
                  'Check Permissions',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              const SizedBox(height: 12),

              ElevatedButton(
                onPressed: _testImmediateNotification,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.all(16),
                ),
                child: const Text(
                  'Test Immediate Notification',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              const SizedBox(height: 12),

              ElevatedButton(
                onPressed: _testScheduledNotification,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  padding: const EdgeInsets.all(16),
                ),
                child: const Text(
                  'Test Scheduled Notification (5s)',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],

            const SizedBox(height: 30),
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Run "Check Permissions" first\n'
              '2. Try "Test Immediate Notification"\n'
              '3. Try "Test Scheduled Notification"\n'
              '4. Check console logs for detailed info\n'
              '5. Make sure device volume is up',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
